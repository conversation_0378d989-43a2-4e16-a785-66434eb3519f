{"version": 3, "file": "forum.js", "sources": ["../src/forum/model/ButtonsCustomization.ts", "../src/forum/index.ts"], "sourcesContent": ["import Model from \"flarum/common/Model\";\r\n\r\nexport default class ButtonsCustomization extends Model {\r\n  id!: () => string | undefined;\r\n  name!: () => string;\r\n  icon!: () => string;\r\n  color!: () => string;\r\n  url!: () => string;\r\n  sort!: () => number;\r\n}\r\n\r\nObject.assign(ButtonsCustomization.prototype, {\r\n  id: Model.attribute<string>(\"id\"),\r\n  name: Model.attribute<string>(\"name\"),\r\n  icon: Model.attribute<string>(\"icon\"),\r\n  color: Model.attribute<string>(\"color\"),\r\n  url: Model.attribute<string>(\"url\"),\r\n  sort: Model.attribute<number>(\"sort\"),\r\n});\r\n", "import app from 'flarum/forum/app';\r\nimport ButtonsCustomization from \"./model/ButtonsCustomization\";\r\n\r\napp.initializers.add('client1-buttons-customization', () => {\r\n  app.store.models.buttonsCustomizationList = ButtonsCustomization;\r\n});"], "names": ["ButtonsCustomization", "Model", "app"], "mappings": "4BAEA,MAAqBA,UAA6BC,CAAM,CAOxD,CAEA,OAAO,OAAOD,EAAqB,UAAW,CAC5C,GAAIC,EAAM,UAAkB,IAAI,EAChC,KAAMA,EAAM,UAAkB,MAAM,EACpC,KAAMA,EAAM,UAAkB,MAAM,EACpC,MAAOA,EAAM,UAAkB,OAAO,EACtC,IAAKA,EAAM,UAAkB,KAAK,EAClC,KAAMA,EAAM,UAAkB,MAAM,CACtC,CAAC,ECfDC,EAAI,aAAa,IAAI,gCAAiC,IAAM,CAC1DA,EAAI,MAAM,OAAO,yBAA2BF,CAC9C,CAAC"}